-- =====================================================
-- DATABASE CONNECTION & SYNCHRONIZATION VERIFICATION
-- =====================================================
-- Professional verification script for Tindahan Store database
-- Use this to diagnose synchronization issues between localhost and Supabase
-- 
-- 🎯 PURPOSE: Verify database connection and check for missing records
-- 📅 CREATED: 2025-07-29
-- 🔧 USAGE: Run this in Supabase SQL Editor to verify your setup
-- =====================================================

-- =====================================================
-- 1. CONNECTION VERIFICATION
-- =====================================================
SELECT 
    'Database Connection Successful' as status,
    current_database() as database_name,
    current_user as connected_user,
    now() as connection_time;

-- =====================================================
-- 2. TABLE STRUCTURE VERIFICATION
-- =====================================================
SELECT 
    'Table Structure Check' as verification_type,
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name IN ('customer_debts', 'customer_payments', 'customers', 'products')
ORDER BY table_name, ordinal_position;

-- =====================================================
-- 3. RECENT DEBT RECORDS CHECK
-- =====================================================
-- Check for recent debt records (including Dave Mejos if it exists)
SELECT 
    'Recent Debt Records' as record_type,
    customer_name,
    customer_family_name,
    product_name,
    product_price,
    quantity,
    total_amount,
    debt_date,
    created_at,
    updated_at
FROM customer_debts 
ORDER BY created_at DESC 
LIMIT 20;

-- =====================================================
-- 4. SEARCH FOR SPECIFIC CUSTOMER (Dave Mejos)
-- =====================================================
-- Check if Dave Mejos exists in the database
SELECT 
    'Dave Mejos Search Results' as search_type,
    customer_name,
    customer_family_name,
    product_name,
    total_amount,
    debt_date,
    created_at
FROM customer_debts 
WHERE customer_name ILIKE '%dave%' 
   OR customer_family_name ILIKE '%mejos%'
   OR customer_name ILIKE '%mejos%'
   OR customer_family_name ILIKE '%dave%';

-- =====================================================
-- 5. CUSTOMER BALANCE VERIFICATION
-- =====================================================
-- Check customer balances view
SELECT 
    'Customer Balances' as balance_type,
    customer_name,
    customer_family_name,
    total_debt,
    total_payments,
    remaining_balance,
    balance_status,
    payment_percentage,
    last_debt_date,
    last_payment_date
FROM customer_balances 
ORDER BY last_debt_date DESC NULLS LAST
LIMIT 15;

-- =====================================================
-- 6. DATABASE STATISTICS
-- =====================================================
-- Get table record counts
SELECT 
    'Database Statistics' as stats_type,
    'Products' as table_name,
    COUNT(*) as record_count
FROM products
UNION ALL
SELECT 
    'Database Statistics',
    'Customers',
    COUNT(*)
FROM customers
UNION ALL
SELECT 
    'Database Statistics',
    'Customer Debts',
    COUNT(*)
FROM customer_debts
UNION ALL
SELECT 
    'Database Statistics',
    'Customer Payments',
    COUNT(*)
FROM customer_payments;

-- =====================================================
-- 7. TROUBLESHOOTING QUERIES
-- =====================================================
-- Check for any constraints or triggers that might affect inserts
SELECT 
    'Constraint Check' as check_type,
    constraint_name,
    table_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'customer_debts';

-- Check for any recent errors in the logs (if accessible)
-- Note: This might not work in all Supabase environments
-- SELECT * FROM pg_stat_activity WHERE state = 'active';

-- =====================================================
-- 8. SAMPLE INSERT TEST (OPTIONAL)
-- =====================================================
-- Uncomment the following to test if inserts work properly
-- WARNING: This will add a test record to your database

/*
-- Test insert (uncomment to run)
INSERT INTO customer_debts (
    customer_name,
    customer_family_name,
    product_name,
    product_price,
    quantity,
    debt_date,
    notes
) VALUES (
    'Test',
    'Customer',
    'Test Product',
    50.00,
    1,
    CURRENT_DATE,
    'Database verification test - safe to delete'
);

-- Verify the test insert
SELECT 
    'Test Insert Verification' as test_type,
    customer_name,
    customer_family_name,
    product_name,
    total_amount,
    created_at
FROM customer_debts 
WHERE customer_name = 'Test' AND customer_family_name = 'Customer'
ORDER BY created_at DESC;
*/

-- =====================================================
-- 9. ENVIRONMENT CHECK
-- =====================================================
-- Check database version and settings
SELECT 
    'Environment Info' as info_type,
    version() as postgresql_version,
    current_setting('timezone') as timezone,
    current_setting('DateStyle') as date_style;

-- =====================================================
-- VERIFICATION COMPLETE
-- =====================================================
-- 🎯 NEXT STEPS:
-- 1. Review the results above
-- 2. Look for Dave Mejos in the search results
-- 3. Check if record counts match your expectations
-- 4. If Dave Mejos is missing, there's a synchronization issue
-- 5. Check your application's environment variables
-- 6. Verify API endpoints are working correctly
-- =====================================================
